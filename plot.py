import numpy as np
import matplotlib.pyplot as plt

def plot_droplet_flame(Y_O_inf, nu, q, T_s, L_v):
    """
    根据给定的无量纲参数绘制液滴火焰形态。

    参数:
    Y_O_inf (float): 环境中的氧化剂质量分数
    nu (float): 化学计量系数
    q (float): 无量纲燃烧热
    T_s (float): 液滴表面温度
    L_v (float): 无量纲蒸发潜热
    """

    # 计算质量流率 M
    B_M = (1 + q * Y_O_inf / nu - T_s) / L_v
    M = np.log(1 + B_M)

    # 计算火焰位置 r_f
    r_f = M / np.log(1 + Y_O_inf / nu)

    # 定义径向坐标范围
    # 我们将从液滴表面 (r=1) 开始绘制
    r_inner = np.linspace(1, r_f, 500)
    r_outer = np.linspace(r_f, 4 * r_f, 500)
    r = np.concatenate((r_inner, r_outer))

    # 初始化质量分数和温度数组
    Y_F = np.zeros_like(r)
    Y_O = np.zeros_like(r)
    T = np.zeros_like(r)

    # 计算火焰内部的解 (r < r_f)
    Y_F[r < r_f] = 1 - (1 + Y_O_inf / nu) * np.exp(-M / r[r < r_f])
    Y_O[r < r_f] = 0
    T[r < r_f] = T_s - L_v + (1 + q / nu * Y_O_inf - (T_s - L_v)) * np.exp(-M / r[r < r_f])


    # 计算火焰外部的解 (r > r_f)
    Y_F[r >= r_f] = 0
    Y_O[r >= r_f] = -nu + (Y_O_inf + nu) * np.exp(-M / r[r >= r_f])
    T[r >= r_f] = T_s + q - L_v + (1 - q - (T_s - L_v)) * np.exp(-M / r[r >= r_f])


    # 绘制结果
    plt.figure(figsize=(10, 6))
    plt.plot(r, Y_F, label=r'$Y_F$ (燃料质量分数)')
    plt.plot(r, Y_O, label=r'$Y_O$ (氧化剂质量分数)')
    plt.plot(r, T, label=r'$T$ (温度)')
    plt.axvline(x=r_f, color='r', linestyle='--', label=f'火焰位置 $r_f$ = {r_f:.2f}')

    # 设置图表格式
    plt.xlabel('无量纲径向距离 r')
    plt.ylabel('无量纲值')
    plt.title('液滴扩散火焰形态')
    plt.legend()
    plt.grid(True)
    plt.xlim(0, 4 * r_f)
    plt.ylim(bottom=0)
    plt.show()

# 设置无量纲参数示例值
Y_O_inf = 0.21  # 空气中氧气的典型质量分数
nu = 8 / 9        # 化学计量系数示例值
q = 25e6 / (27 * 1.0 * 300)          # 无量纲燃烧热示例值
T_s = 2655 / 300         # 无量纲液滴表面温度示例值
L_v = 3         # 无量纲蒸发潜热示例值

# 调用函数绘制图形
plot_droplet_flame(Y_O_inf, nu, q, T_s, L_v)

