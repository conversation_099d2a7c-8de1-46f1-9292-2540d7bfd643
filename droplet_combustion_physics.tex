\documentclass[10pt,a4paper]{article}
\usepackage[final]{neurips_2024}
\usepackage{graphicx}
\usepackage{float}
\usepackage{geometry}
\usepackage{hyperref}
\usepackage{cite}
\usepackage{array}
\usepackage{color}
\usepackage{booktabs}
%% Bibliography
\usepackage{natbib}
%% Enumerate
\usepackage{enumitem} % for [leftmargin=*]
%% Indent first paragraph
% \usepackage{indentfirst}
% \setlength{\parindent}{1em}
%% Math 
\usepackage{amsmath}
\usepackage{amssymb}
\usepackage{mathtools} % for \coloneq

\geometry{margin=1in}

\title{Theoretical Modeling of Droplet Diffusion Flame}

\author{<PERSON><PERSON>, Shen Fang}
\date{\today}

\begin{document}

\numberwithin{equation}{section} % equation number as 1.1, 1.2, ...
\numberwithin{figure}{section}

\maketitle

\begin{abstract}
This document presents a comprehensive physical model and computational framework for simulating the combustion of single-component m-xylene (C$_8$H$_{10}$) droplets in a quiescent oxidizing environment. The model incorporates detailed heat and mass transfer processes, gas-phase species transport, and thermochemical properties based on kinetic theory. The numerical implementation employs finite difference methods for solving the coupled heat and mass transfer equations within the droplet, while the gas-phase is treated using quasi-steady assumptions with detailed property calculations.
\end{abstract}

\section{Introduction}

Droplet combustion is a fundamental process in spray combustion systems, including diesel engines, gas turbines, and industrial burners. Understanding the physical mechanisms governing droplet evaporation, mixing, and combustion is crucial for optimizing combustion efficiency and reducing emissions. This work focuses on the combustion of m-xylene droplets, which serves as a surrogate fuel for complex hydrocarbon mixtures.

\section{Gas Phase Model}

\subsection{Assumptions}

The mathematical model is based on the following key assumptions: (1) Spherically symmetric droplet with uniform internal properties; (2) Quasi-steady gas-phase with rapid equilibration compared to droplet lifetime; (3) Single-step global combustion reaction: $\nu_F F + \nu_O O \rightarrow \nu_P P$; (4) Ideal gas behavior for all gas-phase species; (5) No radiation heat transfer; (6) Constant pressure environment.

\subsection{Governing Equations}

Under the quasi-steady state assumption, The governing equations of the gas phase are simplified to steady-state forms as
\begin{equation} \left\{
\begin{aligned}
\rho u \frac{\partial T}{\partial r} - \frac{\lambda}{c_p} \frac{1}{r^2} \frac{\partial}{\partial r} \left( r^2 \frac{\partial T}{\partial r} \right) &= \frac{Q}{c_p} \omega \\
\rho u \frac{\partial Y_F}{\partial r} - \frac{\rho \mathcal{D}_F}{r^2} \frac{\partial}{\partial r} \left( r^2 \frac{\partial Y_F}{\partial r} \right) &= -\nu_F W_F \omega \\
\rho u \frac{\partial Y_O}{\partial r} - \frac{\rho \mathcal{D}_O}{r^2} \frac{\partial}{\partial r} \left( r^2 \frac{\partial Y_O}{\partial r} \right) &= -\nu_O W_O \omega \\
\rho u \frac{\partial Y_P}{\partial r} - \frac{\rho \mathcal{D}_P}{r^2} \frac{\partial}{\partial r} \left( r^2 \frac{\partial Y_P}{\partial r} \right) &= \nu_P W_P \omega \\
\rho u \frac{\partial Y_N}{\partial r} - \frac{\rho \mathcal{D}_N}{r^2} \frac{\partial}{\partial r} \left( r^2 \frac{\partial Y_N}{\partial r} \right) &= 0 \\
\end{aligned} \right.
\end{equation}
The reaction rate is expressed with the Arrhenius form
\begin{equation}
\omega = B \left(\frac{\rho_F Y_F}{W_F}\right) \left(\frac{\rho_F Y_F}{W_F}\right) e^{-E/\mathcal{R}T}
\end{equation}
Taking the mass flow rate $\dot{m} = 4 \pi r^2 \rho u$ as constant in the quasi-steady assumption, we have
\begin{equation} \left\{
\begin{aligned}
\frac{\dot{m}}{4 \pi r^2} \frac{\partial T}{\partial r} - \frac{\lambda}{c_p} \frac{1}{r^2} \frac{\partial}{\partial r} \left( r^2 \frac{\partial T}{\partial r} \right) &= \frac{Q}{c_p} \omega \\
\frac{\dot{m}}{4 \pi r^2} \frac{\partial Y_F}{\partial r} - \frac{\rho \mathcal{D}_F}{r^2} \frac{\partial}{\partial r} \left( r^2 \frac{\partial Y_F}{\partial r} \right) &= -\nu_F W_F \omega \\
\frac{\dot{m}}{4 \pi r^2} \frac{\partial Y_O}{\partial r} - \frac{\rho \mathcal{D}_O}{r^2} \frac{\partial}{\partial r} \left( r^2 \frac{\partial Y_O}{\partial r} \right) &= -\nu_O W_O \omega \\
\frac{\dot{m}}{4 \pi r^2} \frac{\partial Y_P}{\partial r} - \frac{\rho \mathcal{D}_P}{r^2} \frac{\partial}{\partial r} \left( r^2 \frac{\partial Y_P}{\partial r} \right) &= \nu_P W_P \omega \\
\frac{\dot{m}}{4 \pi r^2} \frac{\partial Y_N}{\partial r} - \frac{\rho \mathcal{D}_N}{r^2} \frac{\partial}{\partial r} \left( r^2 \frac{\partial Y_N}{\partial r} \right) &= 0 \\
\end{aligned} \right.
\end{equation}
With the following transformations
\begin{equation}
\rho = \hat{\rho} \rho_\infty, \quad T = \hat{T} T_\infty, \quad 
r = \hat{r} r_s, \quad \omega = \hat{\omega} \frac{B \rho_\infty^2}{W_F W_O \mathcal{D}}
\end{equation}
where $\mathcal{D} = \frac{\rho_\infty^2 r_s^2 \nu_F B}{(\lambda / c_p) W_O}$ is the Damk\"ohler number. The governing equations can be written as the non-dimensional form
\begin{equation} \left\{
\begin{aligned}
\frac{\dot{m} c_p}{4 \pi r_s \lambda} \frac{1}{\hat{r}^2} \frac{\partial \hat{T}}{\partial \hat{r}} - \frac{1}{\hat{r}^2} \frac{\partial}{\partial \hat{r}} \left( \hat{r}^2 \frac{\partial \hat{T}}{\partial \hat{r}} \right) &= \frac{Q}{\nu_F W_F c_p T_\infty} \hat{\omega} \\
\frac{\dot{m} c_p}{4 \pi r_s \lambda} \frac{1}{\hat{r}^2} \frac{\partial Y_F}{\partial \hat{r}} - \hat{\rho} \frac{\rho_\infty \mathcal{D}_F c_p}{\lambda} \frac{1}{\hat{r}^2} \frac{\partial}{\partial \hat{r}} \left( \hat{r}^2 \frac{\partial Y_F}{\partial \hat{r}} \right) &= -\hat{\omega} \\
\frac{\dot{m} c_p}{4 \pi r_s \lambda} \frac{1}{\hat{r}^2} \frac{\partial Y_O}{\partial \hat{r}} - \hat{\rho} \frac{\rho_\infty \mathcal{D}_O c_p}{\lambda} \frac{1}{\hat{r}^2} \frac{\partial}{\partial \hat{r}} \left( \hat{r}^2 \frac{\partial Y_O}{\partial \hat{r}} \right) &= -\frac{\nu_O W_O}{\nu_F W_F} \hat{\omega} \\
\frac{\dot{m} c_p}{4 \pi r_s \lambda} \frac{1}{\hat{r}^2} \frac{\partial Y_P}{\partial \hat{r}} - \hat{\rho} \frac{\rho_\infty \mathcal{D}_P c_p}{\lambda} \frac{1}{\hat{r}^2} \frac{\partial}{\partial \hat{r}} \left( \hat{r}^2 \frac{\partial Y_P}{\partial \hat{r}} \right) &= \frac{\nu_P W_P}{\nu_F W_F} \hat{\omega} \\
\frac{\dot{m} c_p}{4 \pi r_s \lambda} \frac{1}{\hat{r}^2} \frac{\partial Y_N}{\partial \hat{r}} - \hat{\rho} \frac{\rho_\infty \mathcal{D}_N c_p}{\lambda} \frac{1}{\hat{r}^2} \frac{\partial}{\partial \hat{r}} \left( \hat{r}^2 \frac{\partial Y_N}{\partial \hat{r}} \right) &= 0 \\
\end{aligned} \right.
\end{equation}
Define the coefficients
\begin{equation}
M = \frac{\dot{m} c_p}{4 \pi r_s \lambda}, 
\quad Le_i = \frac{\lambda}{\hat{\rho} \rho_\infty \mathcal{D}_i c_p} (i = F, O, P, N), 
\quad q = \frac{Q}{\nu_F W_F c_p T_\infty}, 
\quad \nu = \frac{\nu_O W_O}{\nu_F W_F}
\end{equation}
with the mass conservation relation $\nu_P W_P = \nu_O W_O + \nu_F W_F$, the governing equations can be written as (where we have omitted the hat symbol for simplicity as in the following)
\begin{equation} \left\{
\begin{aligned}
\frac{M}{r^2} \frac{\partial T}{\partial r} - \frac{1}{r^2} \frac{\partial}{\partial r} \left( r^2 \frac{\partial T}{\partial r} \right) &= q \omega \\
\frac{M}{r^2} \frac{\partial Y_F}{\partial r} - \frac{Le_F^{-1}}{r^2} \frac{\partial}{\partial r} \left( r^2 \frac{\partial Y_F}{\partial r} \right) &= -\omega \\
\frac{M}{r^2} \frac{\partial Y_O}{\partial r} - \frac{Le_O^{-1}}{r^2} \frac{\partial}{\partial r} \left( r^2 \frac{\partial Y_O}{\partial r} \right) &= -\nu \omega \\
\frac{M}{r^2} \frac{\partial Y_P}{\partial r} - \frac{Le_P^{-1}}{r^2} \frac{\partial}{\partial r} \left( r^2 \frac{\partial Y_P}{\partial r} \right) &= (\nu + 1) \omega \\
\frac{M}{r^2} \frac{\partial Y_N}{\partial r} - \frac{Le_N^{-1}}{r^2} \frac{\partial}{\partial r} \left( r^2 \frac{\partial Y_N}{\partial r} \right) &= 0 \\
\end{aligned} \right.
\end{equation}
Assume that the Lewis numbers are all unity, we can greatly simplify the governing equations. To solve the equations, we define the conserved scalars
\begin{equation}
\beta_1 = \nu Y_F - Y_O, \quad \beta_2 = T + \frac{q}{\nu} Y_O, 
\quad \beta_3 = Y_O + \frac{\nu}{\nu + 1} Y_P, \quad \beta_4 = Y_N
\end{equation}
The governing equation for every scalar $\beta_i$ are the same as 
\begin{equation}
\frac{M}{r^2} \frac{\partial \beta}{\partial r} 
- \frac{1}{r^2} \frac{\partial}{\partial r} \left( r^2 \frac{\partial \beta}{\partial r} \right) = 0
\label{equ:ge_beta}
\end{equation}

\subsection{General Solution for Variable Gas Property}

The general governing equation for the conserved scalar $\beta$ can be written as
\begin{equation}
\frac{\dot{m}}{4 \pi r_s} \frac{\partial \beta}{\partial r} - \frac{\lambda}{c_p} \frac{\partial}{\partial r} \left( r^2 \frac{\partial \beta}{\partial r} \right) = 0
\end{equation}
Integrating from $r$ to $+\infty$, we have
\begin{equation}
\frac{\dot{m}}{4 \pi r_s} \beta - \frac{\lambda}{c_p} r^2 \frac{\partial \beta}{\partial r} = \frac{\dot{m}}{4 \pi r_s} C_1 
\Rightarrow 
\frac{\dot{m}}{4 \pi r_s} \frac{c_p}{\lambda r^2} \mathrm{d} r = \frac{\mathrm{d} \beta}{\beta - C_1}
\end{equation}
Define the function $\Lambda(r) = \left( \int_r^\infty \frac{c_p}{\lambda r'^2} \mathrm{d} r' \right)^{-1}$, we can integrate from $r_s$ to $r$ to obtain
\begin{equation}
\ln \frac{\beta - C_1}{\beta_s - C_1}
= \frac{\dot{m}}{4 \pi r_s} \left( \frac{1}{\Lambda(r_s)} - \frac{1}{\Lambda(r)} \right)
\end{equation}
For the variable gas property and unity Lewis number condition, we have
\begin{equation}
\frac{\dot{m}}{4 \pi r_s \Lambda(r_s)} = \ln (1 + B_M)
\end{equation}
where $B_M$ is the Spalding number. With the boundary condition $1 / \Lambda(r_\infty) = 0$, we have
\begin{equation}
C_1 = \frac{\beta_s - \beta_\infty}{B_M} + \beta_s
\end{equation}
the final solution for $\beta(r)$ reads
\begin{equation}
\frac{\beta(r) - \beta_\infty}{\beta_s - \beta_\infty} = -\frac{1 + B_M}{B_M} 
\left[ \exp \left( -\frac{\Lambda(r_s) \ln(1 + B_M)}{\Lambda(r)} \right) - 1 \right] 
\coloneqq f(r)
\end{equation}

\subsection{Specific Solutions for Variable Gas Property}

The boundary conditions for $\beta_1$ and $\beta_2$ are
\begin{equation}
\begin{aligned}
&\beta_1(r_s) = \nu Y_{F,s} - Y_{O,s} = \nu Y_{F,s}, 
\quad \beta_1(r_\infty) = \nu Y_{F,\infty} - Y_{O,\infty} = \nu Y_{F,\infty} \\
&\beta_2(r_s) = T_{s} + \frac{q}{\nu} Y_{O,s} = T_s, 
\quad \beta_2(r_\infty) = 1 + \frac{q}{\nu} Y_{O,\infty} 
\end{aligned}
\end{equation}
Considering the Burke-Schumann limit that an infinitely-thin flame sheet stays at $r_f$and fuel and oxidizer are separated by the flame sheet, the species and temperature profiles can be expressed as
\begin{equation}
\begin{aligned}
Y_F &= \left\{ \begin{aligned} 
- \frac{Y_{O,\infty}}{\nu} + \left(Y_{F,s} + \frac{Y_{O,\infty}}{\nu}\right) f(r), &\quad r < r_f \\
0, &\quad r > r_f 
\end{aligned} \right. \\
Y_O &= \left\{ \begin{aligned} 
0, &\quad r < r_f \\
Y_{O,\infty} - \left(\nu Y_{F,s} + Y_{O,\infty}\right) f(r), &\quad r > r_f 
\end{aligned} \right. \\
T &= \left\{ \begin{aligned} 
1 + \frac{q}{\nu} Y_{O,\infty} + \left(T_s - 1 - \frac{q}{\nu} Y_{O,\infty}\right) f(r), &\quad r < r_f \\
1 + \left(T_s - 1 - q Y_{F,s}\right) f(r), &\quad r > r_f 
\end{aligned} \right. 
\end{aligned}
\end{equation}
We can get the position of flame sheet as the position where the profiles become zero
\begin{equation}
\frac{Y_{O,\infty}}{\nu Y_{F,s} + Y_{O,\infty}} = f(r_f) = -\frac{1 + B_M}{B_M} 
\left[ \exp \left( -\frac{\ln(1 + B_M)}{r_f} \right) - 1 \right]
\end{equation}
Given the expression for Spalding number $B_M = \frac{Y_{O,\infty} / \nu - Y_{F,s}}{1 - Y_{F,s}}$, we have
\begin{equation}
r_f = \frac{\ln(1 + B_M)}{\ln \left( 1 + Y_{O,\infty} / \nu \right)}
\end{equation}

\subsection{General Solution for Constant Gas Property}

The boundary conditions are 
\begin{equation}
\begin{aligned}
& M Y_{F,s} - Le_F^{-1} \left. \frac{\mathrm{d} Y_F}{\mathrm{d} r} \right|_{s} = M, \quad
M Y_{O,s} - Le_O^{-1} \left. \frac{\mathrm{d} Y_O}{\mathrm{d} r} \right|_{s} = 0, \quad
\left. \frac{\mathrm{d} T}{\mathrm{d} r} \right|_{s} = M L_v \\
& T(r_s) = T_s, \quad T_\infty = \rho_\infty = 1, \quad Y_{O}(\infty) = Y_{O,\infty}, 
\quad Y_{F,\infty} = 0
\end{aligned}
\end{equation}
For the governing equations of conserved scalar $\beta_i$ (\hyperref[equ:ge_beta]{2.9}), we have the general solution as 
\begin{equation}
\beta(r) = C_1 + C_2 e^{-\frac{C_3}{r}}
\end{equation}
The terms in the equation becomes
\begin{equation}
\frac{\mathrm{d} \beta}{\mathrm{d} r} = \frac{C_2 C_3}{r^2} e^{-\frac{C_3}{r}}, \quad
\frac{\mathrm{d}}{\mathrm{d} r} \left( r^2 \frac{\mathrm{d} \beta}{\mathrm{d} r} \right) = \frac{C_2 C_3^2}{r^2} e^{-\frac{C_3}{r}}
\end{equation}
Substituting into the governing equation, we have
\begin{equation}
\frac{M}{r^2} \frac{C_2 C_3}{r^2} e^{-\frac{C_3}{r}} - \frac{1}{r^2} \frac{C_2 C_3^2}{r^2} e^{-\frac{C_3}{r}} = 0 \Rightarrow C_3 = M
\end{equation}
For the scalar $\beta_1$, the boundary conditions read
\begin{equation}
\begin{aligned}
C_1 + C_2 &= \beta_\infty = \nu Y_{F,\infty} - Y_{O,\infty} = -Y_{O,\infty} \\
M C_1 + M C_2 e^{-M} &= M \beta_s = \left. \frac{\mathrm{d} \beta}{\mathrm{d} r} \right|_{s} + \nu M = 
C_2 M e^{-M} + \nu M \\
& \Rightarrow C_1 = \nu, \quad C_2 = -Y_{O,\infty} - \nu
\end{aligned}
\end{equation}
For the scalar $\beta_2$, the boundary conditions read
\begin{equation}
\begin{aligned}
C_1 + C_2 &= \beta_\infty = 1 + \frac{q}{\nu} Y_{O,\infty} \\
M \frac{q}{\nu} Y_{O,s} + M L_v &= \left. \frac{\mathrm{d} \beta}{\mathrm{d} r} \right|_{s} = C_2 M e^{-M} \\
C_1 + C_2 e^{-M} &= \beta_s = T_s + \frac{q}{\nu} Y_{O,s} \\
& \Rightarrow C_1 = T_s - L_v, \quad C_2 = 1 + \frac{q}{\nu} Y_{O,\infty} - (T_s - L_v)
\end{aligned}
\end{equation}
The solutions for the scalars are
\begin{equation} \left \{
\begin{aligned}
\nu Y_F - Y_O &= \nu - \left(Y_{O,\infty} + \nu\right) e^{-\frac{M}{r}} \\
T + \frac{q}{\nu} Y_O &= T_s - L_v + \left(1 + \frac{q}{\nu} Y_{O,\infty} - (T_s - L_v)\right) e^{-\frac{M}{r}}
\end{aligned} \right.
\end{equation}
The flame position is defined as the position where $\beta_1$ vanishes
\begin{equation}
r_f = \frac{M}{\ln \left( 1 + Y_{O,\infty} / \nu \right)}
\end{equation}
The solutions for the species and temperature are
\begin{equation} 
\begin{aligned}
Y_F &= \left\{ \begin{aligned} 
1 - (1 + Y_{O,\infty} / \nu) e^{-\frac{M}{r}}, &\quad r < r_f \\
0, &\quad r > r_f 
\end{aligned} \right. \\
Y_O &= \left\{ \begin{aligned} 
0, &\quad r < r_f \\
-\nu + \left(Y_{O,\infty} + \nu\right) e^{-\frac{M}{r}}, &\quad r > r_f 
\end{aligned} \right. \\
T &= \left\{ \begin{aligned} 
T_s - L_v + \left(1 + \frac{q}{\nu} Y_{O,\infty} - (T_s - L_v)\right) e^{-\frac{M}{r}}, &\quad r < r_f \\
T_s + q - L_v + \left(1 - q - (T_s - L_v)\right) e^{-\frac{M}{r}}, &\quad r > r_f 
\end{aligned} \right. 
\end{aligned} 
\end{equation}
With the boundary condition $T(r_s) = T_s$, we can calculated the mass flow rate
\begin{equation}
M = \ln \left( 1 + \frac{1 + q Y_{O,\infty} / \nu - T_s}{L_v} \right), \quad
B_M \coloneqq \frac{1 + q Y_{O,\infty} / \nu - T_s}{L_v}
\end{equation}


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\section{Gas Phase Model under Convection}

\subsection{Governing Equations}

We consider the droplet diffusion flame under convection of oxidizers. The first assumption is different from that of the model without convection -- the droplet diffusion flame is deformed into spherically asymmetric shape. Other assumptions remain the same except that the diffusion coefficients for all species are the same $\mathcal{D} = \mathcal{D}_F = \mathcal{D}_O$. The governing equations are in the two-dimensional $(r, \theta)$ coordinates as
\begin{equation} \left\{
\begin{aligned}
\rho u_r \frac{\partial T}{\partial r} + \rho \frac{u_\theta}{r} \frac{\partial T}{\partial \theta} - \frac{\lambda}{c_p} \nabla^2 T &= \frac{Q_c}{c_p} \omega \\
\rho u_r \frac{\partial Y_F}{\partial r} + \rho \frac{u_\theta}{r} \frac{\partial Y_F}{\partial \theta} - \rho \mathcal{D} \nabla^2 Y_F &= -\nu_F W_F \omega \\
\rho u_r \frac{\partial Y_O}{\partial r} + \rho \frac{u_\theta}{r} \frac{\partial Y_O}{\partial \theta} - \rho \mathcal{D} \nabla^2 Y_O &= -\nu_O W_O \omega \\
\end{aligned} \right.
\end{equation}
where the Laplacian operator is defined as
\begin{equation}
\nabla^2 \coloneqq \frac{1}{r^2} \frac{\partial}{\partial r} \left( r^2 \frac{\partial}{\partial r} \right) + \frac{1}{r^2} \frac{\partial}{\partial \mu} \left[ \left( 1 - \mu^2 \right) \frac{\partial}{\partial \mu} \right], \quad \mu = \cos \theta
\end{equation}
With the following transformations and the definition of Peclet number $Pe = U_\infty r_s / \mathcal{D} \coloneqq 1/\varepsilon$
\begin{equation}
u = \hat{u} U_\infty, \quad T = -\hat{T} \frac{Q_c}{\nu_P W_P c_p}, \quad 
Y_i = \frac{\nu_i W_i}{\nu_P W_P} (i=O,F), \quad
r = \hat{r} r_s, \quad \omega = \hat{\omega} \frac{\rho}{\nu_P W_P} \frac{r_s^2}{\mathcal{D}}
\end{equation}
the governing equations can be written as the non-dimensional form
\begin{equation} \left\{
\begin{aligned}
\hat{u}_r \frac{\partial \hat{T}}{\partial \hat{r}} + \frac{\hat{u}_\theta}{\hat{r}} \frac{\partial \hat{T}}{\partial \theta} - Le \varepsilon \nabla^2 \hat{T} &= -\varepsilon \hat{\omega} \\
\hat{u}_r \frac{\partial \hat{Y}_F}{\partial \hat{r}} + \frac{\hat{u}_\theta}{\hat{r}} \frac{\partial \hat{Y}_F}{\partial \theta} - \varepsilon \nabla^2 \hat{Y}_F &= -\varepsilon \hat{\omega} \\
\hat{u}_r \frac{\partial \hat{Y}_O}{\partial \hat{r}} + \frac{\hat{u}_\theta}{\hat{r}} \frac{\partial \hat{Y}_O}{\partial \theta} - \varepsilon \nabla^2 \hat{Y}_O &= -\varepsilon \hat{\omega} \\
\end{aligned} \right.
\end{equation}
With the unity Lewis number assumption, the equations of species and temperature have the same form as (where we have omitted the hat symbol for simplicity as in the following)
\begin{equation}
u_r \frac{\partial Y_i}{\partial r} + \frac{u_\theta}{r} \frac{\partial Y_i}{\partial \theta} -  \varepsilon \nabla^2 Y_i = -\varepsilon \omega,\quad i = F, O, T
\end{equation}
Introducing the \textbf{Schvab-Zeldovich variable} $q^{(i)} = Y_i - Y_O (i = F,T)$ which satisfies the equations 
\begin{equation}
u_r \frac{\partial q^{(i)}}{\partial r} + \frac{u_\theta}{r} \frac{\partial q^{(i)}}{\partial \theta} -  \varepsilon \nabla^2 q^{(i)} = 0, \quad i = F, T
\label{equ:ge_sz}
\end{equation}
Another assumption is the \textbf{irrotational flow field} such that we can introduce the \textbf{stream function} $\psi$ equation as 
\begin{equation}
D^2 \psi = \left( \frac{\partial^2}{\partial r^2} + \frac{1 - \mu^2}{r^2} \frac{\partial^2}{\partial \mu^2} \right) \psi = 0
\end{equation}
the stream function can be related to the radial and transverse components of velocity as
\begin{equation}
u_r = -\frac{1}{r^2} \frac{\partial \psi}{\partial \mu}, \quad
u_\theta = -\frac{1}{\left(1 - \mu^2\right)^{1/2} r} \frac{\partial \psi}{\partial r}
\end{equation}
This indicates that we can solve both $u_r$ and $u_\theta$ by solving only $\psi$ variable.

\subsection{Boundary Conditions}

The boundary conditions in the far field are
\begin{equation} \left\{
\begin{aligned}
\psi_\infty &= \frac{1}{2} r^2 \left(1 - \mu^2\right) \\
q_\infty^{(F)} &= Y_\infty^{(F)} - Y_\infty^{(O)} = Y_\infty^{(F)} \\
q_\infty^{(T)} &= T_\infty - Y_\infty^{(O)} 
\end{aligned} \right.
\end{equation}

Before driving the boundary conditions at the droplet surface, we need to drive the non-dimensional form of the vaporization mass flow rate at the surface
\begin{equation}
\frac{\dot{m}''_\mathrm{vap}}{\rho \mathcal{D} / r_s} 
= \frac{(\rho u_r)_s}{\rho \mathcal{D} / r_s}  
= \frac{-\rho U_\infty \left. \frac{\partial \psi}{\partial \mu} \right|_{\hat{r}=1}}{\rho \mathcal{D} / r_s}
= -\frac{1}{\varepsilon} \left. \frac{\partial \psi}{\partial \mu} \right|_{\hat{r}=1}
\end{equation}
The dimensional boundary conditions in the droplet surface are
\begin{equation} \left\{
\begin{aligned}
\dot{m}''_{F,s} &= \left( \rho u_r Y_F - \rho \mathcal{D} \frac{\partial Y_F}{\partial r} \right)_s = \dot{m}''_\mathrm{vap} \\
\dot{m}''_{O,s} &= \left( \rho u_r Y_O - \rho \mathcal{D} \frac{\partial Y_O}{\partial r} \right)_s = 0 \\
-\lambda \left. \frac{\partial T}{\partial r} \right|_s &= -\dot{m}''_\mathrm{vap} L_v
\end{aligned} \right.
\end{equation}
Under the unity Lewis number assumption, the non-dimensional form of the boundary conditions are
\begin{equation} \left\{
\begin{aligned}
\left. \left( \hat{u}_r \hat{Y}_F - \varepsilon \frac{\partial \hat{Y}_F}{\partial \hat{r}} \right) \right|_{\hat{r}=1} &= -\frac{\nu_P W_P}{\nu_F W_F} \left. \frac{\partial \psi}{\partial \mu} \right|_{\hat{r}=1} \\
\left. \left( \hat{u}_r \hat{Y}_O - \varepsilon \frac{\partial \hat{Y}_O}{\partial \hat{r}} \right) \right|_{\hat{r}=1} &= 0 \\
\left. \frac{\partial \hat{T}}{\partial \hat{r}} \right|_{\hat{r}=1} &= \frac{1}{\varepsilon} \frac{L_v \nu_P W_P}{Q_c} \left. \frac{\partial \psi}{\partial \mu} \right|_{\hat{r}=1} 
\end{aligned} \right.
\end{equation}
The conditions for SZ variables $q^{(i)}$ are (where we have omitted the hat symbol for simplicity)
\begin{equation} \left\{
\begin{aligned}
\left. \frac{\partial q^{(T)}}{\partial r} \right|_{r=1} &= \frac{1}{\varepsilon} \frac{L_v \nu_P W_P}{Q_c} \left. \frac{\partial \psi}{\partial \mu} \right|_{r=1} \\
\left. \frac{\partial q^{(F)}}{\partial r} \right|_{r=1} &= \frac{1}{\varepsilon} \left( \frac{\nu_P W_P}{\nu_F W_F} - \left. q^{(F)} \right|_{r=1} \right) \left. \frac{\partial \psi}{\partial \mu} \right|_{r=1} \\
\end{aligned} \right.
\end{equation}
Another condition is the Clausius-Clapeyron relation at the droplet surface as
\begin{equation}
\left. q^{(F)} \right|_{r=1} = \frac{\nu_P W_P}{\nu_F W_F} \exp\left[ \chi \left( \frac{1}{\left. q^{(T)} \right|_{r=1}} - \frac{1}{T_b} \right) \right]
\label{equ:cc}
\end{equation}
where $T_b$ is the non-dimensional boiling temperature and $\chi = \frac{Q_v}{R T_b^2}$ is the non-dimensionalized latent heat of vaporization.

\subsection{Perturbation Scheme}
\label{sec:perturbation}

With the assumption of large Peclet number, we have $\varepsilon \ll 1$. The leading term of the perturbation solution will be appropriate to irrotational flow past a sphere. And the evaporation and diffusion of the fuel from the droplet will make a samll perturbation to the solutions. 

The solution for the stream function can be written as
\begin{equation}
\psi = \frac{1}{2} \left(r^2 - \frac{1}{r}\right) + f(\varepsilon) \sum_{n=0}^\infty A_n r^{-n} Q_n(\mu), 
\quad Q_n(\mu) = \int_{-1}^\mu P_n(\mu_1) \mathrm{d} \mu_1
\end{equation}
where $P_n(\cdot)$ is the Legendre polynomial of degree $n$. The boundary conditions at the surface becomes
\begin{equation} \left\{
\begin{aligned}
\left. \frac{\partial q^{(T)}}{\partial r} \right|_{r=1} &= \frac{f(\varepsilon)}{\varepsilon} \frac{L_v \nu_P W_P}{Q_c} A(\mu) \\
\left. \frac{\partial q^{(F)}}{\partial r} \right|_{r=1} &= \frac{f(\varepsilon)}{\varepsilon} \left( \frac{\nu_P W_P}{\nu_F W_F} - \left. q^{(F)} \right|_{r=1} \right) A(\mu) \\
\end{aligned} \right.
\end{equation}
where $A(\mu) = \sum_{n} A_n P_n(\mu)$  is a measure of radial velocity at the droplet surface due to the evaporation of the fuel, which we often call the \textbf{Stefan flow}. We consider the {\color{blue}boundary region} such that the radial coordinate can be defined by
\begin{equation}
r = 1 + \eta g(\varepsilon) \label{equ:stretched}
\end{equation}
where $\eta$ can be regarded as the stretched normal coordinate. The inner dependent solution for SZ variables are
\begin{equation}
q^{(i)} (r,\mu) = \hat{q}^{(i)}_0 (r,\mu) + \mathcal{O}(\varepsilon)
\end{equation}
The boundary conditions can be written as 
\begin{equation} \left\{
\begin{aligned}
\frac{1}{g(\varepsilon)} \left. \frac{\partial \hat{q}_0^{(T)}}{\partial \eta} \right|_{\eta=0} &= \frac{f(\varepsilon)}{\varepsilon} \frac{L_v \nu_P W_P}{Q_c} A(\mu) \\
\frac{1}{g(\varepsilon)} \left. \frac{\partial \hat{q}_0^{(F)}}{\partial \eta} \right|_{\eta=0} &= \frac{f(\varepsilon)}{\varepsilon} \left( \frac{\nu_P W_P}{\nu_F W_F} - \left. \hat{q}_0^{(F)} \right|_{\eta=0} \right) A(\mu) \\
\end{aligned} \right.
\end{equation}
To make the scaling balance, we have the relation $f(\varepsilon) g(\varepsilon) = \varepsilon$. By incorporating the stretched normal coordinate (\hyperref[equ:stretched]{3.17}), the terms in governing equations for SZ variables (\hyperref[equ:ge_sz]{3.6}) become
\begin{equation} \left\{
\begin{aligned}
u_r \frac{\partial q^{(i)}}{\partial r} &= -\frac{1}{r^2} \left[ \left( r^2 - \frac{1}{r} \right) \mu + f(\varepsilon) A(\mu) \right] \frac{1}{g(\varepsilon)} \frac{\partial \hat{q}_0^{(i)}}{\partial \eta} = \left[ 3\eta \mu - \frac{f(\varepsilon)}{g(\varepsilon)} A(\mu) \right] \frac{\partial \hat{q}_0^{(i)}}{\partial \eta} \\
\frac{u_\theta}{r} \frac{\partial q^{(i)}}{\partial \theta} &= -\frac{u_\theta \sin\theta}{r} \frac{\partial \hat{q}_0^{(i)}}{\partial \mu} = \frac{\sin\theta \left(r + \frac{1}{2r^2}\right)}{\left(1 - \mu^2\right)^{1/2}r^2} \left(1 - \mu^2\right) \frac{\partial q^{(i)}}{\partial \mu} = \frac{3}{2} \left(1 - \mu^2\right) \frac{\partial \hat{q}_0^{(i)}}{\partial \mu} \\
\varepsilon \nabla^2 q^{(i)} &\approx \varepsilon \frac{\partial^2 \hat{q}_0^{(i)}}{\partial r^2} = \frac{\varepsilon}{g(\varepsilon)^2} \frac{\partial^2 \hat{q}_0^{(i)}}{\partial \eta^2} 
\end{aligned} \right.
\end{equation}
We have the governing equation under the perturbation scheme as
\begin{equation}
\left[ 3\eta \mu - \frac{f(\varepsilon)}{g(\varepsilon)} A(\mu) \right] \frac{\partial \hat{q}_0^{(i)}}{\partial \eta} + \frac{3}{2} \left(1 - \mu^2\right) \frac{\partial \hat{q}_0^{(i)}}{\partial \mu} - \frac{\varepsilon}{g(\varepsilon)^2} \frac{\partial^2 \hat{q}_0^{(i)}}{\partial \eta^2} = \mathcal{O}(g(\varepsilon))
\end{equation}
To make the scaling balance, we have the relation $f(\varepsilon) = g(\varepsilon)$ and $\varepsilon = g(\varepsilon)^2$. Without the loss of generality, we can set $f(\varepsilon) = g(\varepsilon) = \varepsilon^{1/2}$. The governing equation becomes
\begin{equation}
\left[ 3\eta \mu - A(\mu) \right] \frac{\partial \hat{q}_0^{(i)}}{\partial \eta} + \frac{3}{2} \left(1 - \mu^2\right) \frac{\partial \hat{q}_0^{(i)}}{\partial \mu} - \frac{\partial^2 \hat{q}_0^{(i)}}{\partial \eta^2} = 0
\label{equ:ge_sz_inner}
\end{equation}
and the boundary conditions becomes
\begin{equation} \left\{
\begin{aligned}
\left. \frac{\partial \hat{q}_0^{(T)}}{\partial \eta} \right|_{\eta=0} &= \frac{L_v \nu_P W_P}{Q_c} A(\mu) \\
\left. \frac{\partial \hat{q}_0^{(F)}}{\partial \eta} \right|_{\eta=0} &= \left( \frac{\nu_P W_P}{\nu_F W_F} - \left. \hat{q}_0^{(F)} \right|_{\eta=0} \right) A(\mu) \\
\end{aligned} \right.
\label{equ:bc_sz_inner}
\end{equation}

\subsection{Lowest Order Inner Problem}

Consider the front stagnation point formally expanded in powers of $t = 1 + \mu$, we have
\begin{equation} \left\{
\begin{aligned}
\hat{q}_0^{(i)}(\eta, \mu) &= Z_0^{(i)}(\eta) + Z_1^{(i)}(\eta) t + Z_2^{(i)}(\eta) t^2 + \cdots \\
A(\mu) &= \hat{A}(t) = a_0 + a_1 t + a_2 t^2 + \cdots
\end{aligned} \right.
\end{equation}
Substituting into the governing equation (\hyperref[equ:ge_sz_inner]{3.22}), we have
\begin{equation}
(3\eta (t - 1) - a_0 - a_1 t) \left(Z_0' + Z_1' t\right) + \frac{3}{2} (2t - t^2) Z_1 - (Z_0'' + Z_1'' t) = 0
\end{equation}
Equating the coefficients of different powers of $t$, we have
\begin{equation} \left\{
\begin{aligned}
Z_0'' + (3\eta + a_0) Z_0' &= 0 \\
Z_1'' + (3\eta + a_0) Z_1' &= 3 Z_1 + (3\eta - a_1) Z_0' \\
\end{aligned} \right.
\label{equ:ge_sz_inner_lowest}
\end{equation}
Substituting into the surface boundary conditions (\hyperref[equ:bc_sz_inner]{3.23}) and (\hyperref[equ:cc]{3.14}), we have
\begin{equation} \left\{
\begin{aligned}
Z_0^{(T) \prime}(0) + Z_1^{(T) \prime}(0) t &= \frac{L_v \nu_P W_P}{Q_c} (a_0 + a_1 t) \\
Z_0^{(F) \prime}(0) + Z_1^{(F) \prime}(0) t &= \left[ \frac{\nu_P W_P}{\nu_F W_F} - \left(Z_0^{(F)}(0) + Z_1^{(F)}(0) t\right) \right] (a_0 + a_1 t) \\
Z_0^{(F)}(0) + Z_1^{(F)}(0) t &= \frac{\nu_P W_P}{\nu_F W_F} \exp\left[ \chi \left( \frac{1}{Z_0^{(T)}(0) + Z_1^{(T)}(0) t} - \frac{1}{T_b} \right) \right] \\
\end{aligned} \right.
\end{equation}
Equating the coefficients of different powers of $t$, we have
\begin{equation} \left\{
\begin{aligned}
Z_n^{(T) \prime}(0) &= \frac{L_v \nu_P W_P}{Q_c} a_n, & n = 0, 1, \cdots \\
Z_n^{(F) \prime}(0) &= \left( \frac{\nu_P W_P}{\nu_F W_F} - Z_0^{(F)}(0) \right) a_n, & n = 0, 1, \cdots \\
Z_0^{(F)}(0) &= \frac{\nu_P W_P}{\nu_F W_F} \exp\left[ \chi \left( \frac{1}{Z_0^{(T)}(0)} - \frac{1}{T_b} \right) \right] \\
Z_n^{(F)}(0) &= 0 = Z_n^{(T)}(0), & n = 1, 2, \cdots
\end{aligned} \right.
\label{equ:sf_sz_inner_lowest}
\end{equation}
The last condition is derived from the fact that $\left. \partial \psi / \partial \mu \right|_{\eta = 0} = 0$ and the one-to-one mapping $q_0^{(i)} = q_0^{(i)} (\psi)$. With the physically reasonable assumption that $\chi \gg 1$, the Clausius-Clapeyron relation becomes
\begin{equation}
Z_0^{(T)}(0) = T_b + \mathcal{O}(1/\chi)
\label{equ:st_sz_inner_lowest}
\end{equation}
The far-field boundary conditions are
\begin{equation}
Z_0^{(i)}(\infty) = q_\infty^{(i)}, \quad Z_n^{(i)}(\infty) = 0 (n = 1, 2, \cdots)
\label{equ:ff_sz_inner_lowest}
\end{equation}

\subsection{Inner Problem Solutions}

To solve the inner problem, we can integrate the first order governing equation (\hyperref[equ:ge_sz_inner_lowest]{3.26}). Let $W(\eta) = Z_0^{(i) \prime}(\eta)$. The equation becomes a first-order separable ODE for $W$ as
\begin{equation}
W' = -(3\eta + a_0) W
\end{equation}
Integrating of the equation gives
\begin{equation}
\ln(W) = -\left(\frac{3}{2}\eta^2 + a_0\eta\right) + \ln C_1
\Rightarrow
W(\eta) = Z_0^{(i) \prime} = C_1 \exp\left(-\frac{3}{2}\eta^2 - a_0\eta\right)
\end{equation}
To find $Z_0^{(i)}(\eta)$, we integrate $W(\eta)$ one more time as
\begin{equation}
Z_0^{(i)}(\eta) = C_1 \int \exp\left(-\frac{3}{2}\eta^2 - a_0\eta\right) d\eta + C_2
\label{eq:integral_form}
\end{equation}
The integral can be expressed in terms of the complementary error function, $\text{erfc}(x)$, by completing the square in the exponent:
\begin{equation}
-\frac{3}{2}\eta^2 - a_0\eta 
= -\frac{3}{2}\left(\eta^2 + \frac{2}{3}a_0\eta\right) 
= -\frac{3}{2}\left(\eta + \frac{1}{3}a_0\right)^2 + \frac{1}{6}a_0^2
\end{equation}
Substituting this back into the integral we have
\begin{equation}
\exp\left(\frac{1}{6}a_0^2\right) \exp\left[-\frac{3}{2}\left(\eta + \frac{1}{3}a_0\right)^2\right] d\eta
\end{equation}
Let $x = \sqrt{\frac{3}{2}}\left(\eta + \frac{1}{3}a_0\right)$, so $d\eta = \sqrt{\frac{2}{3}}dx$. The integral becomes
\begin{equation}
e^{a_0^2 / 6} \sqrt{\frac{2}{3}} \int e^{-x^2} dx 
= e^{a_0^2 / 6} \sqrt{\frac{2}{3}} \left(-\frac{\sqrt{\pi}}{2}\text{erfc}(x) \right) 
= -\sqrt{\frac{\pi}{6}} e^{a_0^2 / 6} \text{erfc}(x)
\end{equation}
Thus, the general solution for $Z_0^{(i)}(\eta)$ has the form 
\begin{equation}
Z_0^{(i)}(\eta) = -\sqrt{\frac{\pi}{6}} e^{a_0^2 / 6} C_1 \cdot \text{erfc}\left\{\sqrt{\frac{3}{2}}\left(\eta + \frac{1}{3}a_0\right)\right\} + C_2
\label{equ:Zi0}
\end{equation}

From the far-field boundary conditions (\hyperref[equ:ff_sz_inner_lowest]{3.30}), we have
\begin{equation}
q_\infty^{(i)} = -\sqrt{\frac{\pi}{6}} e^{a_0^2 / 6} C_1 \cdot \text{erfc}(\infty) + C_2 \Rightarrow C_2 = q_\infty^{(i)}
\end{equation}
From the surface temperature condition (\hyperref[equ:st_sz_inner_lowest]{3.29}), we have
\begin{equation}
T_b = q_\infty^{(T)} + K^{(T)} \cdot \text{erfc}\left\{\sqrt{\frac{3}{2}}\left(\frac{1}{3}a_0\right)\right\} = q_\infty^{(T)} + K^{(T)} \cdot \text{erfc}\left(\frac{a_0}{\sqrt{6}}\right)
\end{equation}
where we have defined $K^{(T)} = -\sqrt{\pi / 6} e^{a_0^2 / 6} C_1$. This gives an expression for $K^{(T)}$ as
\begin{equation}
K^{(T)} = \frac{T_b - q_\infty^{(T)}}{\text{erfc}(a_0/\sqrt{6})} = \frac{T_b - (Y_\infty^{(T)} - Y_\infty^{(O)})}{\text{erfc}(a_0/\sqrt{6})}
\label{equ:KT_1}
\end{equation}
From the surface heat flux condition (\hyperref[equ:sf_sz_inner_lowest]{3.28}), we have
\begin{equation} 
Z_0^{(T) \prime}(0) = K^{(T)} \left[ -\sqrt{\frac{\pi}{6}} e^{a_0^2 / 6} \right]^{-1} e^0 = -K^{(T)}\sqrt{\frac{6}{\pi}} e^{a_0^2 / 6} = \frac{L_v \nu_P W_P}{Q_c} a_0
\end{equation}
We have the expression for $K^{(T)}$ as
\begin{equation}
K^{(T)} = -\sqrt{\frac{\pi}{6}} e^{a_0^2 / 6} \frac{L_v \nu_P W_P}{Q_c} a_0
\label{equ:KT_2}
\end{equation}
From the surface heat fluxs, we have
\begin{equation} \left\{
\begin{aligned}
Z_0^{(T) \prime}(0) &= -K^{(T)} \sqrt{\frac{6}{\pi}} \exp\left(-\frac{1}{6}a_0^2\right) \\
Z_0^{(F) \prime}(0) &= -K^{(F)} \sqrt{\frac{6}{\pi}} \exp\left(-\frac{1}{6}a_0^2\right) 
\end{aligned} \right.
\end{equation}
We have the relation
\begin{equation}
\frac{Z_0^{(F) \prime}(0)}{Z_0^{(T) \prime}(0)} = \frac{K^{(F)}}{K^{(T)}}
\end{equation}
Substituting into the surface boundary conditions (\hyperref[equ:sf_sz_inner_lowest]{3.28}), we have
\begin{equation}
q_\infty^{(F)} + K^{(F)} \text{erfc}\left(\frac{a_0}{\sqrt{6}}\right) = Z_0^{(F)}(0) = \frac{\nu_P W_P}{\nu_F W_F} - \frac{K^{(F)}}{K^{(T)}} \frac{L_v \nu_P W_P}{Q_c}
\end{equation}
Substituting the expression for coefficient $K^{(T)}$ (\hyperref[equ:KT_1]{3.40}), we have
\begin{equation}
\begin{aligned}
\frac{\nu_P W_P}{\nu_F W_F} - q_\infty^{(F)} 
&= K^{(F)} \left[ \frac{L_v \nu_P W_P}{Q_c} \frac{\text{erfc}(a_0/\sqrt{6})}{T_b - q_\infty^{(T)}} + \text{erfc}\left(\frac{a_0}{\sqrt{6}}\right) \right] \\
&= K^{(F)} \text{erfc}\left(\frac{a_0}{\sqrt{6}}\right) \left[ \frac{L_v \nu_P W_P / Q_c + T_b - q_\infty^{(T)}}{T_b - q_\infty^{(T)}} \right]
\end{aligned}
\end{equation}
We have the solution for $K^{(F)}$ as
\begin{equation}
\begin{aligned}
K^{(F)} &= \frac{T_b - q_\infty^{(T)}}{\frac{L_v \nu_P W_P}{Q_c} + T_b - q_\infty^{(T)}} 
\frac{\frac{\nu_P W_P}{\nu_F W_F} - q_\infty^{(F)}}{\text{erfc}\left(\frac{a_0}{\sqrt{6}}\right)} \\
&= \frac{T_b - T_\infty + Y_\infty^{(O)}}{\frac{L_v \nu_P W_P}{Q_c} + T_b - q_\infty^{(T)}} 
\frac{\frac{\nu_P W_P}{\nu_F W_F} - T_\infty + Y_\infty^{(O)}}{\text{erfc}\left(\frac{a_0}{\sqrt{6}}\right)}
\label{equ:KF}
\end{aligned}
\end{equation}
The constant $a_0$ can be solved by equating the two expression for $K^{(T)}$ (\hyperref[equ:KT_1]{3.40}) and (\hyperref[equ:KT_2]{3.42}) as
\begin{equation}
\frac{T_b - (Y_\infty^{(T)} - Y_\infty^{(O)})}{\text{erfc}(a_0/\sqrt{6})} 
= -\sqrt{\frac{\pi}{6}} e^{a_0^2 / 6} \frac{L_v \nu_P W_P}{Q_c} a_0
\end{equation}

\subsection{Full Problem Solution}

In the previous section we have reached the solutions for inner problem in the stagnation point. The solutions determined some coefficients which can be used in the following solutions for the full problem under the boundary region assumption described in \hyperref[sec:perturbation]{Section 3.3}. The solutions for the SZ variables of the full problem take the form
\begin{equation}
\hat{q}_0^{(i)} = \hat{q}_\infty^{(i)} + K^{(i)} f(\eta, \mu)
\end{equation}
where the constants $K^{(i)}$ are determined in the inner problem solutions as (\hyperref[equ:KT_1]{3.40}) and (\hyperref[equ:KF]{3.47}). The function $f(\eta, \mu)$ is to be determined and satisfy the governing equation (\hyperref[equ:ge_sz_inner]{3.22}) as
\begin{equation}
\left[ 3\eta \mu - A(\mu) \right] \frac{\partial f}{\partial \eta} + \frac{3}{2} \left(1 - \mu^2\right) \frac{\partial f}{\partial \mu} - \frac{\partial^2 f}{\partial \eta^2} = 0, \quad
\eta > 0, \mu \in [-1, 1]
\end{equation}
The boundary condition for $f(\eta, \mu)$ are
\begin{equation}
f(\infty, \mu) = 0, \quad
f(\eta, -1) = \text{erfc}\left[\sqrt{\frac{3}{2}}\left(\eta + \frac{a_0}{3}\right)\right], \quad
f(0, \mu) = \text{erfc}\left(\frac{a_0}{\sqrt{6}}\right)
\end{equation}
Substituting the solution into the boundary condition (\hyperref[equ:bc_sz_inner]{3.23}), we have
\begin{equation}
K^{(T)} \left. \frac{\partial f}{\partial \eta} \right|_{\eta=0} = \frac{L_v \nu_P W_P}{Q_c} A(\mu)
\end{equation}
As we have know the value of $f(\eta, -1)$, we can reformulate the equation as a marching scheme
\begin{equation}
\frac{\partial f}{\partial \mu} = D(\mu) \frac{\partial^2 f}{\partial \eta^2} 
+ V(\eta, \mu) \frac{\partial f}{\partial \eta}, \quad D = \frac{2}{3(1-\mu^2)}, 
\quad V = \frac{K^{(T)}}{mL} \left(\frac{\partial f}{\partial \eta}\right)_{\eta = 0} - 3\mu \eta
\end{equation}
Firstly, we try to solve it with explicit \textbf{DuFort-Frankel} scheme, where the terms are discretized as
\begin{equation}
\frac{f_{i}^{j+1} - f_{i}^{j-1}}{2\Delta \mu} 
= D(\mu_j) \frac{f_{i+1}^{j} - 2\frac{f_{i}^{j+1} + f_{i}^{j-1}}{2} + f_{i-1}^{j}}{\Delta \eta^2} 
+ V(\eta_i, \mu_j) \frac{f_{i+1}^{j} - f_{i-1}^{j}}{2\Delta \eta}
\end{equation}
Take $r = \frac{\Delta\mu D}{2\Delta\eta^2}$, we have
\begin{equation}
f_{i}^{j+1} (1+2r) = f_{i}^{j-1} (1-2r) + 2r (f_{i+1}^{j} + f_{i-1}^{j}) 
+ \frac{\Delta\mu V}{\Delta \eta} (f_{i+1}^{j} - f_{i-1}^{j})
\end{equation}
The solution is shown in Figure \hyperref[fig:f_mu_eta]{3.1}.
However, it can be seen that the DuFort-Frankel scheme is not stable for large values of $\eta$ when $\mu$ is fairly close to unity. Therefore, we try to use the \textbf{Crank-Nicolson} scheme with $(\partial f/\partial \mu)_{\eta=0}$ obtained from the DuFort-Frankel scheme, where the terms are discretized as
\begin{equation}
\frac{f_{i}^{j+1} - f_{i}^{j}}{\Delta\mu} 
= \frac{D(\mu_j)}{2} \frac{f_{i+1}^{j+1} - 2f_{i}^{j+1} + f_{i-1}^{j+1} + f_{i+1}^{j} - 2f_{i}^{j} + f_{i-1}^{j}}{2\Delta \eta^2} 
+ \frac{V(\eta_i, \mu_j)}{2} \frac{f_{i+1}^{j+1} - f_{i-1}^{j+1} + f_{i+1}^{j} - f_{i-1}^{j}}{2\Delta \eta}
\end{equation}
Take $r = \frac{\Delta\mu D}{2\Delta\eta^2}$ and $s = \frac{\Delta\mu V}{4\Delta \eta}$, we have 
\begin{equation}
f_{i}^{j+1} (1+2r) - (r+s) f_{i+1}^{j+1} - (r-s) f_{i-1}^{j+1} 
= f_{i}^{j} (1-2r) + (r+s) f_{i+1}^{j} + (r-s) f_{i-1}^{j}
\end{equation}
In the matrix form, we have
\begin{equation}
\begin{bmatrix}
(1+2r)_1 & -(r+s)_1 & 0 & \cdots & 0 \\
-(r-s)_2 & (1+2r)_2 & -(r+s)_2 & \cdots & 0 \\
\vdots & \vdots & \ddots & \vdots & \vdots \\
0 & 0 & \cdots & (1+2r)_{N-2} & -(r+s)_{N-2} \\
0 & 0 & \cdots & -(r-s)_{N-1} & (1+2r)_{N-1}
\end{bmatrix} \begin{bmatrix}
f_1^{j+1} \\
f_2^{j+1} \\
\vdots \\
f_{N-2}^{j+1} \\
f_{N-1}^{j+1}
\end{bmatrix} 
= \begin{bmatrix}
b_1 \\
b_2 \\
\vdots \\
b_{N-2} \\
b_{N-1}
\end{bmatrix} - \begin{bmatrix}
- (r-s)_1 f_0^{j+1} \\
0 \\
\vdots \\
0 \\
- (r+s)_{N-1} f_{N}^{j+1}
\end{bmatrix}
\end{equation}
Solving the linear equation, we can obtain $f_{i}^{j+1}$. The solution is also shown in Figure \hyperref[fig:f_mu_eta]{3.1} and proves to be stable throughout the field of interest.

\begin{figure}[h]
\centering
\includegraphics[width=1.0\textwidth]{../figs/f_mu_eta.pdf}
\caption{Solution of the diffusion equation}
\label{fig:f_mu_eta}
\end{figure}



%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\section{Physical and Chemical Properties}

\subsection{Fuel Characteristics}

m-Xylene (meta-xylene, C$_8$H$_{10}$) is an aromatic hydrocarbon with the following properties:
\begin{itemize}[leftmargin=*]
\item Molecular weight: $\mathrm{MW}_F = 106.16$ g/mol
\item Boiling point: $T_b = 412.3$ K at 1 atm
\item Critical temperature is calculated with the method of Joback from Lydersen's group contribution scheme (Page 2.3 in \citep{poling2001properties}) as
\begin{equation}
T_c = T_b \left[ 0.584 + 0.965 \sum_k N_k(tck) - \left( \sum_k N_k(tck) \right)^2 \right]^{-1}
\end{equation}
\item Heat of vaporization: $Q_v = 42.65 \times 10^4$ J/kg
\item Heat of combustion: $Q_c = 43.24 \times 10^6$ J/kg
\item Combustion chemistry: The global combustion reaction for m-xylene is:
\begin{equation}
\text{C}_8\text{H}_{10} + 10.5 \text{O}_2 \rightarrow 8 \text{CO}_2 + 5 \text{H}_2\text{O}
\end{equation}
The stoichiometric oxygen-to-fuel mass ratio is:
\begin{equation}
\nu = \frac{10.5 \times MW_{O_2}}{MW_{C_8H_{10}}} = \frac{10.5 \times 32.0}{106.16} = 3.166
\end{equation}
\end{itemize}

\subsection{Gas Properties}

\begin{itemize}[leftmargin=*]
\item Heat capacities: Species-specific heat capacities are calculated using NASA polynomial fits
\begin{equation}
\frac{C_p}{R} = a_1 + a_2 T + a_3 T^2 + a_4 T^3 + a_5 T^4
\end{equation}
J/kg/K, where $a_i$ are the NASA polynomial coefficients for each species.

\item Thermal conductivity: Assuming that the individual species conductivities are composed of translational, rotational, and vibrational contributions, the gas mixture thermal conductivity is calculated by Warnatz's method (Page 516 in \citep{kee2017chemically}) as
\begin{equation}
\lambda_k = \frac{\eta_k}{\mathrm{MW}_k} \left( f_{\mathrm{trans}} C_{v,\mathrm{trans}} + f_{\mathrm{rot}} C_{v,\mathrm{rot}} + f_{\mathrm{vib}} C_{v,\mathrm{vib}} \right)
\end{equation}
W/m/K, where the thermal conductivity factors are:
\begin{equation} \left\{
\begin{aligned}
& f_{\mathrm{trans}} = \frac{5}{2} \left( 1 - \frac{2}{\pi} \frac{C_{v,\mathrm{rot}}}{C_{v,\mathrm{trans}}} \frac{A}{B} \right), \quad
f_{\mathrm{rot}} = \frac{\rho D_{kk}}{\eta_k} \left( 1 + \frac{2}{\pi} \frac{A}{B} \right), \quad
f_{\mathrm{vib}} = \frac{\rho D_{kk}}{\eta_k} \\
& A = \frac{5}{2} - \rho \frac{D_{kk}}{\eta_k}, \quad
B = Z_{\mathrm{rot}} + \frac{2}{\pi} \left( \frac{5}{3} \frac{C_{v,\mathrm{rot}}}{R} + \rho \frac{D_{kk}}{\eta_k} \right)
\end{aligned} \right.
\end{equation}
The mixture thermal conductivity uses the harmonic-arithmetic mean:
\begin{equation}
\lambda_{\mathrm{mix}} = \frac{1}{2} \left( \sum_k X_k \lambda_k + \frac{1}{\sum_k \frac{X_k}{\lambda_k}} \right)
\end{equation}

\end{itemize}

\subsection{Liquid Properties}

The liquid properties of m-xylene are calculated using the following correlations:
\begin{itemize}[leftmargin=*]
\item Density: The correlation for density is defined as a second-order polynomial
\begin{equation}
\rho_l = a+bT + cT^2
\end{equation}
kg/m$^3$, where $a=995.58, b=-0.1449, c=-0.0011$
\item Heat capacities: The specific heat capacity is calculated with the Method of Ruzicka and Domalski (Page 6.19 in \citep{poling2001properties})
\begin{equation}
c_p = \frac{R_g}{\mathrm{MW}_F} \left[A + B\frac{T}{100} + D\left(\frac{T}{100}\right)^2\right]
\end{equation}
J/kg/K, where $A=26.3916, B=-7.7335, D=2.8289$
\item Thermal conductivity: The thermal conductivity is calculated using the correlation of Latini, et al. (Page 10.44 in \citep{poling2001properties}) as
\begin{equation}
\lambda_L = \frac{A (1 - T_r^{0.38})}{T_r^{1/6}} \quad A = \frac{A^* T_b^\alpha}{1000\mathrm{MW_F}^{\beta} T_c^{\gamma}}
\end{equation}
W/m/K, where $T_r = T/T_c, A^*=0.00319, \alpha=1.2, \beta=0.5, \gamma=0.167$
\end{itemize}


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\section{Discretization Schemes}

The heat and mass transfer partial differential equations (PDEs) are solved using
\begin{itemize}[leftmargin=*]
\item \textbf{Time discretization}: Crank-Nicolson scheme (semi-implicit, second-order accurate)
\item \textbf{Space discretization}: Central difference for diffusion and convection
\item \textbf{Solution method}: Implicit-Euler method with LU decomposition
\item \textbf{Coordinate system}: Moving coordinate $\eta = r/r_s(t)$ following droplet regression
\end{itemize}

\subsection{Heat Transfer}

The heat transfer PDE in the moving coordinate system is given by
\begin{equation}
\frac{\partial T}{\partial t} - \left(\frac{r_s\dot{r}_s}{r_s^2}\eta + \frac{2 \alpha_d}{r_s^2 \eta}\right)\frac{\partial T}{\partial \eta} = \frac{\alpha_d}{r_s^2} \frac{\partial^2 T}{\partial \eta^2}
\end{equation}
For single-component droplet, we set $S_i = 0$ (no chemical reactions inside the droplet). The equation is discretized using finite difference method (FDM) with grid spacing $\Delta \eta = \Delta x$. 

For interior points ($i = 1, 2, ..., N-1$), \textbf{Crank-Nicolson} scheme is used such that the discretized equation reads
\begin{equation}
\begin{aligned}
\frac{T_i^{n+1} - T_i^n}{\Delta t} &- \left(\frac{r_s\dot{r}_s}{r_s^2}\eta_i + \frac{2 \alpha_d}{r_s^2 \eta_i}\right)\frac{1}{2}\left[\frac{T_{i+1}^{n+1} - T_{i-1}^{n+1}}{2\Delta \eta} + \frac{T_{i+1}^{n} - T_{i-1}^{n}}{2\Delta \eta}\right] \\
&= \frac{\alpha_d}{r_s^2} \frac{1}{2} \left[ \frac{T_{i+1}^{n+1} - 2T_i^{n+1} + T_{i-1}^{n+1}}{(\Delta \eta)^2} + \frac{T_{i+1}^{n} - 2T_i^{n} + T_{i-1}^{n}}{(\Delta \eta)^2} \right]
\end{aligned}
\end{equation}
Define the convection coefficient as
\begin{equation}
a_i = -\left(\frac{r_s\dot{r}_s}{r_s^2}\eta_i + \frac{2\alpha_d}{r_s^2 \eta_i}\right) = -\left(\frac{d(r_s^2)/dt}{2r_s^2}\eta_i + \frac{2\alpha_d}{r_s^2 \eta_i}\right)
\end{equation}
the diffusion and time step coefficient as
\begin{equation}
b_i = \frac{\alpha_d}{r_s^2}, 
\quad h_1 = \frac{\Delta t}{\Delta \eta}, 
\quad h_2 = \frac{\Delta t}{(\Delta \eta)^2}
\end{equation}
The Crank-Nicolson discretized equation becomes
\begin{equation}
\begin{aligned}
\frac{T_i^{n+1} - T_i^n}{\Delta t} &+ \frac{a_i}{4\Delta \eta}(T_{i+1}^{n+1} - T_{i-1}^{n+1}) + \frac{a_i}{4\Delta \eta}(T_{i+1}^{n} - T_{i-1}^{n}) \\
&= \frac{b_i}{2(\Delta \eta)^2}(T_{i+1}^{n+1} - 2T_i^{n+1} + T_{i-1}^{n+1}) + \frac{b_i}{2(\Delta \eta)^2}(T_{i+1}^{n} - 2T_i^{n} + T_{i-1}^{n})
\end{aligned}
\end{equation}
Rearranging terms to separate implicit (LHS) and explicit (RHS) parts
\begin{align}
&\left(-\frac{a_i h_1}{4} - \frac{b_i h_2}{2}\right)T_{i-1}^{n+1} + \left(1 + b_i h_2\right)T_i^{n+1} + \left(\frac{a_i h_1}{4} - \frac{b_i h_2}{2}\right)T_{i+1}^{n+1} \\
&= \left(\frac{a_i h_1}{4} + \frac{b_i h_2}{2}\right)T_{i-1}^{n} + \left(1 - b_i h_2\right)T_i^{n} + \left(-\frac{a_i h_1}{4} + \frac{b_i h_2}{2}\right)T_{i+1}^{n}
\end{align}
We have the matrix form equation to be solved
\begin{equation}
\mathbf{A} \mathbf{T}^{n+1} = \mathbf{B}
\end{equation}
where the matrices are given by
\begin{equation} \left\{
\begin{aligned}
A_{i,i-1} &= -\frac{a_i h_1}{4} - \frac{b_i h_2}{2} \\
A_{i,i} &= 1 + b_i h_2 \\
A_{i,i+1} &= \frac{a_i h_1}{4} - \frac{b_i h_2}{2} \\
B_i &= \left(\frac{a_i h_1}{4} + \frac{b_i h_2}{2}\right)T_{i-1}^{n} + \left(1 - b_i h_2\right)T_i^{n} + \left(-\frac{a_i h_1}{4} + \frac{b_i h_2}{2}\right)T_{i+1}^{n}
\end{aligned} \right.
\end{equation}

The boundary conditions for heat transfer are given by 
\begin{equation} \left\{
\begin{aligned}
\left. \frac{\partial T}{\partial r} \right|_{r=0} &= 0 \\
4 \pi r_s^2 \lambda_{g,s} \left. \frac{\partial T}{\partial r} \right|_{r=r_s^+}
&= 4 \pi r_s^2 \lambda_{l} \left. \frac{\partial T}{\partial r} \right|_{r=r_s^-} + \dot{m} L_v
\end{aligned} \right.
\end{equation}
Given the droplet evaporation rate
\begin{equation}
\dot{m} = \frac{4 \pi r_s \bar{\lambda}_g}{\bar{c}_p} \ln(1 + B_m)
\label{equ:evap}
\end{equation}
The droplet surface condition becomes
\begin{equation}
\begin{aligned}
\left. \frac{\partial T}{\partial \eta} \right|_{r=r_s^-} 
&= r_s \frac{\lambda_{g,s}}{\lambda_l} \left. \frac{\partial T}{\partial r} \right|_{r=r_s^-} - \frac{\bar{\lambda}_{g}}{\bar{c}_p \lambda_l} \ln(1+B_m) L_v \\
&= r_s \frac{\lambda_{g,s}}{\lambda_l} \left(T_s - T_\infty - \frac{Y_{O,\infty} Q_c}{\nu \bar{c}_p}\right) \left. \frac{\partial f(r)}{\partial r} \right|_{r=r_s} - \frac{\bar{\lambda}_{g}}{\bar{c}_p \lambda_l} \ln(1+B_m) L_v \\
&= \frac{\lambda_{g,s}}{\lambda_l} \frac{\bar{c}_p (T_\infty - T_s) + Y_{O,\infty} Q_c / \nu}{\bar{c}_p B_m} \ln(1 + B_m) - \frac{\bar{\lambda}_{g}}{\bar{c}_p \lambda_l} \ln(1+B_m) L_v \\
&= \frac{\lambda_{g,s}}{\lambda_l} \frac{\bar{c}_p T_\infty + Y_{O,\infty} Q_c / \nu}{\bar{c}_p B_m} \ln(1 + B_m) - \frac{\bar{\lambda}_{g}}{\bar{c}_p \lambda_l} \ln(1+B_m) L_v - \frac{\lambda_{g,s}}{\lambda_l} \frac{\ln(1 + B_m)}{B_m} T_s \\
&\coloneqq C_0 + C_1 T_s
\end{aligned}
\end{equation}
The discretized conditions are 
\begin{equation} \left\{
\begin{aligned}
\frac{T_1^{n+1} - T_0^{n+1}}{\Delta \eta} &= 0 \\
\frac{T_{N+1}^{n+1} - T_{N}^{n+1}}{\Delta \eta} &= C_0 + C_1 T_{N+1}^{n+1}
\end{aligned} \right.
\end{equation}
The terms can be rearranged to
\begin{equation} \left\{
\begin{aligned}
T_0^{n+1} - T_1^{n+1} &= 0 \\
- T_N^{n+1} + (1 - C_1 \Delta \eta) T_{N+1}^{n+1} &= C_0 \Delta \eta
\end{aligned} \right.
\end{equation}

\subsection{Mass Transfer}

The mass transfer PDE in the moving coordinate is given by
\begin{equation}
\frac{\partial Y_k}{\partial t} - \left(\frac{r_s\dot{r}_s}{r_s^2}\eta + \frac{2 D_k}{r_s^2 \eta}\right)\frac{\partial Y_k}{\partial \eta} = \frac{D_k}{r_s^2} \frac{\partial^2 Y_k}{\partial \eta^2}
\end{equation}
It can be simply noticed that the mass transfer equation has the same form as the heat transfer equation, with $D_k$ replacing $\alpha_d$. Therefore, the same discretization scheme can be applied.

The boundary conditions for mass transfer are given by
\begin{equation} \left\{
\begin{aligned}
\left. \frac{\partial Y_k}{\partial r} \right|_{\eta=0} &= 0 \\
\dot{m} Y_{k,s^-} - 4 \pi r_s^2 \rho_l D_{k,l} \left. \frac{\partial Y_k}{\partial r} \right|_{r=r_s^-} 
&= \dot{m} Y_{k,s^+} - 4 \pi r_s^2 \rho_g D_{k,g} \left. \frac{\partial Y_k}{\partial r} \right|_{r=r_s^+}
\end{aligned} \right.
\end{equation}
Given the droplet evaporation rate (\hyperref[equ:evap]{15.11}), the droplet surface condition becomes
\begin{equation}
\begin{aligned}
\left. \frac{\partial Y_k}{\partial \eta} \right|_{r=r_s^-} 
&= \frac{\bar{\lambda}_g \ln(1+B_m)}{\bar{c}_p \rho_l D_{k,l}} \left(Y_{k,s^-} - Y_{k,s^+}\right) 
+ \frac{r_s \rho_g D_{k,g}}{\rho_l D_{k,l}} \left. \frac{\partial Y_k}{\partial r} \right|_{r=r_s^+} \\
&\approx -\frac{\bar{\lambda}_g \ln(1+B_m)}{\bar{c}_p \rho_l D_{k,l}} \left(1 - Y_{k,s^-}\right) \\
&\coloneqq C_0 - C_0 Y_{k,s^-}
\end{aligned}
\end{equation}
where we used the approximation $\rho_l \gg \rho_g$ and only fuel is evaporized. The discretized conditions are 
\begin{equation} \left\{
\begin{aligned}
\frac{Y_{k,1}^{n+1} - Y_{k,0}^{n+1}}{\Delta \eta} &= 0 \\
\frac{-3 Y_{k,N+1}^{n+1} + 4 Y_{k,N}^{n+1} - Y_{k,N-1}^{n+1}}{-2 \Delta \eta} &= C_0 - C_0 Y_{k,N+1}^{n+1}
\end{aligned} \right.
\end{equation}
where the second-order backward difference $u'(x) = [-3u(x) + 4u(x-h) - u(x-2h)]/(-2 h)$ is used for the gradient. The terms can be rearranged to
\begin{equation} \left\{
\begin{aligned}
Y_{k,0}^{n+1} - Y_{k,1}^{n+1} &= 0 \\
\frac{1}{2} Y_{k,N-1}^{n+1} - 2 Y_{k,N}^{n+1} + \left(\frac{3}{2} + C_0 \Delta \eta\right) Y_{k,N+1}^{n+1} &= C_0 \Delta \eta
\end{aligned} \right.
\end{equation}


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\section{Nomenclature}

\begin{table}[H]
\centering
\begin{tabular}{ll}
\toprule
Symbol & Description \\
\midrule
$B_M$ & Spalding mass transfer number \\
$C_p$ & Specific heat at constant pressure (J/kg·K) \\
$D$ & Mass diffusivity (m$^2$/s) \\
$MW$ & Molecular weight (kg/mol) \\
$r$ & Radial coordinate (m) \\
$r_s$ & Droplet radius (m) \\
$T$ & Temperature (K) \\
$Y_i$ & Mass fraction of species $i$ \\
$X_i$ & Mole fraction of species $i$ \\
$\alpha$ & Thermal diffusivity (m$^2$/s) \\
$\lambda$ & Thermal conductivity (W/m·K) \\
$\nu$ & Stoichiometric ratio \\
$\rho$ & Density (kg/m$^3$) \\
$\omega$ & Chemical source term (kg/m$^3$·s) \\
\bottomrule
\end{tabular}
\caption{Nomenclature}
\end{table}

\bibliographystyle{abbrvnat}
\bibliography{refs}

\end{document}
