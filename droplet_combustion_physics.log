This is pdfTeX, Version 3.141592653-2.6-1.40.24 (TeX Live 2022) (preloaded format=pdflatex 2022.10.21)  10 JUL 2025 15:09
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**droplet_combustion_physics
(./droplet_combustion_physics.tex
LaTeX2e <2021-11-15> patch level 1
L3 programming layer <2022-02-24> (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/base/article.cls
Document Class: article 2021/10/04 v1.4n Standard LaTeX document class
(e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2021/10/04 v1.4n Standard LaTeX file (size option)
)
\c@part=\count185
\c@section=\count186
\c@subsection=\count187
\c@subsubsection=\count188
\c@paragraph=\count189
\c@subparagraph=\count190
\c@figure=\count191
\c@table=\count192
\abovecaptionskip=\skip47
\belowcaptionskip=\skip48
\bibindent=\dimen138
) (./neurips_2024.sty
Package: neurips_2024 2024/03/31 NeurIPS 2024 submission/camera-ready style file
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/environ/environ.sty
Package: environ 2014/05/04 v0.3 A new way to define environments
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/trimspaces/trimspaces.sty
Package: trimspaces 2009/09/17 v1.1 Trim spaces around a token list
)
\@envbody=\toks16
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/natbib/natbib.sty
Package: natbib 2010/09/13 8.31b (PWD, AO)
\bibhang=\skip49
\bibsep=\skip50
LaTeX Info: Redefining \cite on input line 694.
\c@NAT@ctr=\count193
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2014/10/28 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count194
\Gm@cntv=\count195
\c@Gm@tempcnt=\count196
\Gm@bindingoffset=\dimen139
\Gm@wd@mp=\dimen140
\Gm@odd@mp=\dimen141
\Gm@even@mp=\dimen142
\Gm@layoutwidth=\dimen143
\Gm@layoutheight=\dimen144
\Gm@layouthoffset=\dimen145
\Gm@layoutvoffset=\dimen146
\Gm@dimlist=\toks18
)
\@neuripsabovecaptionskip=\skip51
\@neuripsbelowcaptionskip=\skip52
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2021/03/04 v1.4d Standard LaTeX Graphics (DPC,SPQR)
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 107.
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2020/10/05 v1.2a Graphics/color driver for pdftex
))
\Gin@req@height=\dimen147
\Gin@req@width=\dimen148
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count197
\float@exts=\toks19
\float@box=\box50
\@float@everytoks=\toks20
\@floatcapt=\box51
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2022-02-21 v7.00n Hypertext links for LaTeX
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2020-05-10 v1.25 LaTeX kernel commands for general use (HO)
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2019/12/15 v1.18 Key value parser (HO)
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/letltxmacro/letltxmacro.sty
Package: letltxmacro 2019/12/03 v1.6 Let assignment for LaTeX macros (HO)
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/auxhook/auxhook.sty
Package: auxhook 2019-12-17 v1.6 Hooks for auxiliary files (HO)
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2020-10-07 v3.14 Key value format for package options (HO)
)
\@linkdim=\dimen149
\Hy@linkcounter=\count198
\Hy@pagecounter=\count199
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2022-02-21 v7.00n Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/etexcmds/etexcmds.sty
Package: etexcmds 2019/12/15 v1.7 Avoid name clashes with e-TeX commands (HO)
)
\Hy@SavedSpaceFactor=\count266
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2022-02-21 v7.00n Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Hyper figures OFF on input line 4137.
Package hyperref Info: Link nesting OFF on input line 4142.
Package hyperref Info: Hyper index ON on input line 4145.
Package hyperref Info: Plain pages OFF on input line 4152.
Package hyperref Info: Backreferencing OFF on input line 4157.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4390.
\c@Hy@tempcnt=\count267
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip16
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4749.
\XeTeXLinkMargin=\dimen150
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count268
\Field@Width=\dimen151
\Fld@charsize=\dimen152
Package hyperref Info: Hyper figures OFF on input line 6027.
Package hyperref Info: Link nesting OFF on input line 6032.
Package hyperref Info: Hyper index ON on input line 6035.
Package hyperref Info: backreferencing OFF on input line 6042.
Package hyperref Info: Link coloring OFF on input line 6047.
Package hyperref Info: Link coloring with OCG OFF on input line 6052.
Package hyperref Info: PDF/A mode OFF on input line 6057.
LaTeX Info: Redefining \ref on input line 6097.
LaTeX Info: Redefining \pageref on input line 6101.
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count269
\c@Item=\count270
\c@Hfootnote=\count271
)
Package hyperref Info: Driver (autodetected): hpdftex.
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/hyperref/hpdftex.def
File: hpdftex.def 2022-02-21 v7.00n Hyperref driver for pdfTeX
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend package
with kernel methods
)
\Fld@listcount=\count272
\c@bookmark@seq@number=\count273
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2019/12/05 v1.9 Rerun checks for auxiliary files (HO)
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 286.
)
\Hy@SectionHShift=\skip53
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/cite/cite.sty
LaTeX Info: Redefining \cite on input line 302.
LaTeX Info: Redefining \nocite on input line 332.
Package: cite 2015/02/27  v 5.5
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/tools/array.sty
Package: array 2021/10/04 v2.5f Tabular extension package (FMi)
\col@sep=\dimen153
\ar@mcellbox=\box52
\extrarowheight=\dimen154
\NC@list=\toks21
\extratabsurround=\skip54
\backup@length=\skip55
\ar@cellbox=\box53
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/graphics/color.sty
Package: color 2021/12/07 v1.3c Standard LaTeX Color (DPC)
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package color Info: Driver file: pdftex.def on input line 149.
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen155
\lightrulewidth=\dimen156
\cmidrulewidth=\dimen157
\belowrulesep=\dimen158
\belowbottomsep=\dimen159
\aboverulesep=\dimen160
\abovetopsep=\dimen161
\cmidrulesep=\dimen162
\cmidrulekern=\dimen163
\defaultaddspace=\dimen164
\@cmidla=\count274
\@cmidlb=\count275
\@aboverulesep=\dimen165
\@belowrulesep=\dimen166
\@thisruleclass=\count276
\@lastruleclass=\count277
\@thisrulewidth=\dimen167
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/enumitem/enumitem.sty
Package: enumitem 2019/06/20 v3.9 Customized lists
\labelindent=\skip56
\enit@outerparindent=\dimen168
\enit@toks=\toks22
\enit@inbox=\box54
\enit@count@id=\count278
\enitdp@description=\count279
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2021/10/15 v2.17l AMS math features
\@mathmargin=\skip57

For additional information on amsmath, use the `?' option.
(e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks23
\ex@=\dimen169
)) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen170
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2021/08/26 v2.02 operator names
)
\inf@bad=\count280
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count281
\leftroot@=\count282
LaTeX Info: Redefining \overline on input line 399.
\classnum@=\count283
\DOTSCASE@=\count284
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box55
\strutbox@=\box56
\big@size=\dimen171
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count285
\c@MaxMatrixCols=\count286
\dotsspace@=\muskip17
\c@parentequation=\count287
\dspbrk@lvl=\count288
\tag@help=\toks24
\row@=\count289
\column@=\count290
\maxfields@=\count291
\andhelp@=\toks25
\eqnshift@=\dimen172
\alignsep@=\dimen173
\tagshift@=\dimen174
\tagwidth@=\dimen175
\totwidth@=\dimen176
\lineht@=\dimen177
\@envbody=\toks26
\multlinegap=\skip58
\multlinetaggap=\skip59
\mathdisplay@stack=\toks27
LaTeX Info: Redefining \[ on input line 2938.
LaTeX Info: Redefining \] on input line 2939.
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/mathtools/mathtools.sty
Package: mathtools 2022/02/07 v1.28a mathematical typesetting tools
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/tools/calc.sty
Package: calc 2017/05/25 v4.3 Infix arithmetic (KKT,FJ)
\calc@Acount=\count292
\calc@Bcount=\count293
\calc@Adimen=\dimen178
\calc@Bdimen=\dimen179
\calc@Askip=\skip60
\calc@Bskip=\skip61
LaTeX Info: Redefining \setlength on input line 80.
LaTeX Info: Redefining \addtolength on input line 81.
\calc@Ccount=\count294
\calc@Cskip=\skip62
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/mathtools/mhsetup.sty
Package: mhsetup 2021/03/18 v1.4 programming setup (MH)
)
\g_MT_multlinerow_int=\count295
\l_MT_multwidth_dim=\dimen180
\origjot=\skip63
\l_MT_shortvdotswithinadjustabove_dim=\dimen181
\l_MT_shortvdotswithinadjustbelow_dim=\dimen182
\l_MT_above_intertext_sep=\dimen183
\l_MT_below_intertext_sep=\dimen184
\l_MT_above_shortintertext_sep=\dimen185
\l_MT_below_shortintertext_sep=\dimen186
\xmathstrut@box=\box57
\xmathstrut@dim=\dimen187
)
LaTeX Font Info:    Trying to load font information for OT1+ptm on input line 30.
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/psnfss/ot1ptm.fd
File: ot1ptm.fd 2001/06/04 font definitions for OT1/ptm.
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2022-02-07 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count296
\l__pdf_internal_box=\box58
) (./droplet_combustion_physics.aux)
\openout1 = `droplet_combustion_physics.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 30.
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 30.
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 30.
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 30.
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 30.
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 30.
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 30.
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 30.
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 30.
LaTeX Font Info:    ... okay on input line 30.

*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: letterpaper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(72.26999pt, 469.75502pt, 72.26999pt)
* v-part:(T,H,B)=(72.26999pt, 650.43001pt, 72.26999pt)
* \paperwidth=614.295pt
* \paperheight=794.96999pt
* \textwidth=469.75502pt
* \textheight=650.43001pt
* \oddsidemargin=0.0pt
* \evensidemargin=0.0pt
* \topmargin=-37.0pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=10.0pt
* \footskip=30.0pt
* \marginparwidth=57.0pt
* \marginparsep=11.0pt
* \columnsep=10.0pt
* \skip\footins=9.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

*geometry* verbose mode - [ newgeometry ] result:
* driver: pdftex
* paper: letterpaper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(108.405pt, 397.48499pt, 108.40501pt)
* v-part:(T,H,B)=(72.26999pt, 650.43pt, 72.27pt)
* \paperwidth=614.295pt
* \paperheight=794.96999pt
* \textwidth=397.48499pt
* \textheight=650.43pt
* \oddsidemargin=36.13501pt
* \evensidemargin=36.13501pt
* \topmargin=-37.0pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=10.0pt
* \footskip=30.0pt
* \marginparwidth=57.0pt
* \marginparsep=11.0pt
* \columnsep=10.0pt
* \skip\footins=9.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

(e:/Program Files (x86)/texlive/2022/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count297
\scratchdimen=\dimen188
\scratchbox=\box59
\nofMPsegments=\count298
\nofMParguments=\count299
\everyMPshowfont=\toks28
\MPscratchCnt=\count300
\MPscratchDim=\dimen189
\MPnumerator=\count301
\makeMPintoPDFobject=\count302
\everyMPtoPDFconversion=\toks29
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live
))
Package hyperref Info: Link coloring OFF on input line 30.
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2021-04-02 v2.47 Cross-referencing by name of section
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
)
\c@section@level=\count303
)
LaTeX Info: Redefining \ref on input line 30.
LaTeX Info: Redefining \pageref on input line 30.
LaTeX Info: Redefining \nameref on input line 30.
 (./droplet_combustion_physics.out) (./droplet_combustion_physics.out)
\@outlinefile=\write3
\openout3 = `droplet_combustion_physics.out'.

LaTeX Font Info:    Trying to load font information for U+msa on input line 36.
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 36.
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
) [1


{e:/Program Files (x86)/texlive/2022/texmf-var/fonts/map/pdftex/updmap/pdftex.map}] [2] [3] [4] [5] [6] [7] [8] [9]
Overfull \hbox (36.87027pt too wide) detected at line 621
[][][][] \OT1/cmr/m/n/10 = [][] + [][]
 []


Overfull \hbox (74.21509pt too wide) detected at line 655
[][][][] [] \OT1/cmr/m/n/10 = [] \OMS/cmsy/m/n/10 ^^@ []
 []

[10]
<../figs/f_mu_eta.pdf, id=254, 761.72336pt x 393.98067pt>
File: ../figs/f_mu_eta.pdf Graphic file (type pdf)
<use ../figs/f_mu_eta.pdf>
Package pdftex.def Info: ../figs/f_mu_eta.pdf  used on input line 660.
(pdftex.def)             Requested size: 397.48499pt x 205.5865pt.
LaTeX Font Info:    Trying to load font information for TS1+ptm on input line 674.
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/psnfss/ts1ptm.fd
File: ts1ptm.fd 2001/06/04 font definitions for TS1/ptm.
) [11 <../figs/f_mu_eta.pdf>] [12] [13] [14] (./droplet_combustion_physics.bbl) [15] (./droplet_combustion_physics.aux)
Package rerunfilecheck Info: File `droplet_combustion_physics.out' has not changed.
(rerunfilecheck)             Checksum: 15DF892CDD89A621198BAD0697B5BBD7;3741.
 ) 
Here is how much of TeX's memory you used:
 11828 strings out of 478268
 188131 string characters out of 5840330
 514607 words of memory out of 5000000
 29705 multiletter control sequences out of 15000+600000
 483959 words of font info for 56 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 70i,16n,76p,640b,453s stack positions out of 10000i,1000n,20000p,200000b,200000s
{e:/Program Files (x86)/texlive/2022/texmf-dist/fonts/enc/dvips/base/8r.enc}<e:/Program Files (x86)/texlive/2022/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx10.pfb><e:/Program Files (x86)/texlive/2022/texmf-dist/fonts/type1/public/amsfonts/cm/cmex10.pfb><e:/Program Files (x86)/texlive/2022/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi10.pfb><e:/Program Files (x86)/texlive/2022/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi5.pfb><e:/Program Files (x86)/texlive/2022/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi7.pfb><e:/Program Files (x86)/texlive/2022/texmf-dist/fonts/type1/public/amsfonts/cm/cmr10.pfb><e:/Program Files (x86)/texlive/2022/texmf-dist/fonts/type1/public/amsfonts/cm/cmr5.pfb><e:/Program Files (x86)/texlive/2022/texmf-dist/fonts/type1/public/amsfonts/cm/cmr7.pfb><e:/Program Files (x86)/texlive/2022/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy10.pfb><e:/Program Files (x86)/texlive/2022/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy5.pfb><e:/Program Files (x86)/texlive/2022/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy7.pfb><e:/Program Files (x86)/texlive/2022/texmf-dist/fonts/type1/urw/times/utmb8a.pfb><e:/Program Files (x86)/texlive/2022/texmf-dist/fonts/type1/urw/times/utmr8a.pfb><e:/Program Files (x86)/texlive/2022/texmf-dist/fonts/type1/urw/times/utmri8a.pfb>
Output written on droplet_combustion_physics.pdf (15 pages, 296423 bytes).
PDF statistics:
 492 PDF objects out of 1000 (max. 8388607)
 386 compressed objects within 4 object streams
 159 named destinations out of 1000 (max. 500000)
 182 words of extra memory for PDF output out of 10000 (max. 10000000)

